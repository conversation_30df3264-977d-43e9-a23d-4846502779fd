#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Color Editor для файлов dark.style
Позволяет изменять цвета в формате xFFFF9000 в файлах стилей
"""

import sys
import re
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QComboBox, QLabel, QTextEdit, 
                             QColorDialog, QFileDialog, QMessageBox, QGroupBox,
                             QGridLayout, QLineEdit, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor, QPalette, QFont


class ColorPreview(QWidget):
    """Виджет для предварительного просмотра цвета"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(50, 30)
        self.color = QColor(255, 144, 0, 255)  # Цвет по умолчанию FF9000 с альфой FF
        
    def set_color(self, color):
        """Установить цвет для предварительного просмотра"""
        self.color = color
        self.update()
        
    def paintEvent(self, event):
        """Отрисовка цвета"""
        from PyQt5.QtGui import QPainter
        painter = QPainter(self)
        painter.fillRect(self.rect(), self.color)


class ColorTypeManager:
    """Менеджер типов цветов"""
    
    COLOR_TYPES = {
        'BaseColor': 'FF9000',      # Основной цвет
        'BorderColor': 'FFFFFF',    # Цвет границ
        'AccentColor': '00AAFF',    # Акцентный цвет
        'HoverColor': 'FFB84D',     # Цвет при наведении
        'PressedColor': 'E6800A',   # Цвет при нажатии
        'BackgroundColor': '151515', # Цвет фона
        'TextColor': 'FFFFFF',      # Цвет текста
        'SelectionColor': '0FFFFFFF' # Цвет выделения
    }
    
    @classmethod
    def get_color_types(cls):
        """Получить список типов цветов"""
        return list(cls.COLOR_TYPES.keys())
    
    @classmethod
    def get_default_color(cls, color_type):
        """Получить цвет по умолчанию для типа"""
        return cls.COLOR_TYPES.get(color_type, 'FF9000')


class StyleFileParser:
    """Парсер файлов стилей"""
    
    # Регулярное выражение для поиска цветов в формате xFFFF9000
    COLOR_PATTERN = re.compile(r'x([A-Fa-f0-9]{2})([A-Fa-f0-9]{6})')
    
    @classmethod
    def find_colors(cls, content):
        """Найти все цвета в содержимом файла"""
        matches = []
        for match in cls.COLOR_PATTERN.finditer(content):
            alpha = match.group(1)
            color = match.group(2)
            matches.append({
                'full_match': match.group(0),
                'alpha': alpha,
                'color': color,
                'start': match.start(),
                'end': match.end()
            })
        return matches
    
    @classmethod
    def replace_colors(cls, content, old_color, new_color, alpha=None):
        """Заменить цвета в содержимом"""
        if alpha is None:
            # Заменяем только цветовую часть, сохраняя альфу
            pattern = re.compile(rf'x([A-Fa-f0-9]{{2}}){re.escape(old_color)}', re.IGNORECASE)
            return pattern.sub(rf'x\g<1>{new_color}', content)
        else:
            # Заменяем и альфу, и цвет
            pattern = re.compile(rf'x[A-Fa-f0-9]{{2}}{re.escape(old_color)}', re.IGNORECASE)
            return pattern.sub(f'x{alpha}{new_color}', content)


class ColorEditorMainWindow(QMainWindow):
    """Главное окно редактора цветов"""
    
    def __init__(self):
        super().__init__()
        self.file_path = None
        self.file_content = ""
        self.current_colors = []
        
        self.init_ui()
        self.load_default_file()
        
    def init_ui(self):
        """Инициализация пользовательского интерфейса"""
        self.setWindowTitle("Color Editor для dark.style")
        self.setGeometry(100, 100, 800, 600)
        
        # Центральный виджет
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Основной layout
        main_layout = QVBoxLayout(central_widget)
        
        # Группа управления файлом
        file_group = QGroupBox("Файл")
        file_layout = QHBoxLayout(file_group)
        
        self.file_label = QLabel("Файл не загружен")
        self.load_button = QPushButton("Загрузить файл")
        self.save_button = QPushButton("Сохранить")
        self.save_as_button = QPushButton("Сохранить как...")
        
        self.load_button.clicked.connect(self.load_file)
        self.save_button.clicked.connect(self.save_file)
        self.save_as_button.clicked.connect(self.save_file_as)
        
        file_layout.addWidget(self.file_label)
        file_layout.addStretch()
        file_layout.addWidget(self.load_button)
        file_layout.addWidget(self.save_button)
        file_layout.addWidget(self.save_as_button)
        
        # Группа редактирования цветов
        color_group = QGroupBox("Редактирование цветов")
        color_layout = QGridLayout(color_group)
        
        # Комбобокс типов цветов
        color_layout.addWidget(QLabel("Тип цвета:"), 0, 0)
        self.color_type_combo = QComboBox()
        self.color_type_combo.addItems(ColorTypeManager.get_color_types())
        self.color_type_combo.currentTextChanged.connect(self.on_color_type_changed)
        color_layout.addWidget(self.color_type_combo, 0, 1)
        
        # Текущий цвет
        color_layout.addWidget(QLabel("Текущий цвет:"), 1, 0)
        self.current_color_edit = QLineEdit()
        self.current_color_edit.setReadOnly(True)
        color_layout.addWidget(self.current_color_edit, 1, 1)
        
        # Предварительный просмотр текущего цвета
        self.current_color_preview = ColorPreview()
        color_layout.addWidget(self.current_color_preview, 1, 2)
        
        # Новый цвет
        color_layout.addWidget(QLabel("Новый цвет:"), 2, 0)
        self.new_color_edit = QLineEdit()
        color_layout.addWidget(self.new_color_edit, 2, 1)
        
        # Предварительный просмотр нового цвета
        self.new_color_preview = ColorPreview()
        color_layout.addWidget(self.new_color_preview, 2, 2)
        
        # Кнопка выбора цвета
        self.pick_color_button = QPushButton("Выбрать цвет")
        self.pick_color_button.clicked.connect(self.pick_color)
        color_layout.addWidget(self.pick_color_button, 2, 3)
        
        # Альфа канал
        color_layout.addWidget(QLabel("Прозрачность (FF=100%):"), 3, 0)
        self.alpha_edit = QLineEdit("FF")
        self.alpha_edit.setMaxLength(2)
        color_layout.addWidget(self.alpha_edit, 3, 1)
        
        # Кнопка применения
        self.apply_button = QPushButton("Применить изменения")
        self.apply_button.clicked.connect(self.apply_color_changes)
        color_layout.addWidget(self.apply_button, 4, 0, 1, 4)
        
        # Текстовое поле для просмотра содержимого
        content_group = QGroupBox("Содержимое файла")
        content_layout = QVBoxLayout(content_group)
        
        self.content_text = QTextEdit()
        self.content_text.setFont(QFont("Consolas", 10))
        content_layout.addWidget(self.content_text)
        
        # Добавляем группы в основной layout
        main_layout.addWidget(file_group)
        main_layout.addWidget(color_group)
        main_layout.addWidget(content_group)
        
        # Подключаем обработчики
        self.new_color_edit.textChanged.connect(self.update_new_color_preview)
        self.alpha_edit.textChanged.connect(self.update_new_color_preview)
        
        # Инициализируем интерфейс
        self.on_color_type_changed()
        
    def load_default_file(self):
        """Загрузить файл dark.style по умолчанию"""
        default_path = "dark.style"
        if os.path.exists(default_path):
            self.load_file_content(default_path)
            
    def load_file(self):
        """Загрузить файл через диалог"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Выберите файл стиля", "", "Style files (*.style);;All files (*.*)")
        
        if file_path:
            self.load_file_content(file_path)
            
    def load_file_content(self, file_path):
        """Загрузить содержимое файла"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.file_content = f.read()
            
            self.file_path = file_path
            self.file_label.setText(f"Файл: {os.path.basename(file_path)}")
            self.content_text.setPlainText(self.file_content)
            
            # Найти все цвета в файле
            self.current_colors = StyleFileParser.find_colors(self.file_content)
            
            # Обновить интерфейс
            self.update_current_color_display()
            
            QMessageBox.information(self, "Успех", 
                f"Файл загружен. Найдено цветов: {len(self.current_colors)}")
                
        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Не удалось загрузить файл: {str(e)}")

    def save_file(self):
        """Сохранить файл"""
        if not self.file_path:
            self.save_file_as()
            return

        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.write(self.file_content)
            QMessageBox.information(self, "Успех", "Файл сохранен")
        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Не удалось сохранить файл: {str(e)}")

    def save_file_as(self):
        """Сохранить файл как..."""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Сохранить файл как", "", "Style files (*.style);;All files (*.*)")

        if file_path:
            self.file_path = file_path
            self.save_file()
            self.file_label.setText(f"Файл: {os.path.basename(file_path)}")

    def on_color_type_changed(self):
        """Обработчик изменения типа цвета"""
        color_type = self.color_type_combo.currentText()
        default_color = ColorTypeManager.get_default_color(color_type)
        self.new_color_edit.setText(default_color)
        self.update_current_color_display()

    def update_current_color_display(self):
        """Обновить отображение текущего цвета"""
        color_type = self.color_type_combo.currentText()
        default_color = ColorTypeManager.get_default_color(color_type)

        # Найти текущий цвет этого типа в файле
        found_color = None
        for color_info in self.current_colors:
            if color_info['color'].upper() == default_color.upper():
                found_color = color_info
                break

        if found_color:
            self.current_color_edit.setText(f"x{found_color['alpha']}{found_color['color']}")
            # Обновить предварительный просмотр
            color = QColor(f"#{found_color['color']}")
            alpha = int(found_color['alpha'], 16)
            color.setAlpha(alpha)
            self.current_color_preview.set_color(color)
        else:
            self.current_color_edit.setText("Не найден")
            self.current_color_preview.set_color(QColor(128, 128, 128, 128))

    def update_new_color_preview(self):
        """Обновить предварительный просмотр нового цвета"""
        try:
            color_text = self.new_color_edit.text().strip()
            alpha_text = self.alpha_edit.text().strip()

            if len(color_text) == 6 and len(alpha_text) == 2:
                # Проверяем, что это валидные hex значения
                int(color_text, 16)
                alpha = int(alpha_text, 16)

                color = QColor(f"#{color_text}")
                color.setAlpha(alpha)
                self.new_color_preview.set_color(color)
            else:
                self.new_color_preview.set_color(QColor(128, 128, 128, 128))
        except ValueError:
            self.new_color_preview.set_color(QColor(128, 128, 128, 128))

    def pick_color(self):
        """Открыть диалог выбора цвета"""
        # Получить текущий цвет
        try:
            color_text = self.new_color_edit.text().strip()
            if len(color_text) == 6:
                current_color = QColor(f"#{color_text}")
            else:
                current_color = QColor(255, 144, 0)  # Цвет по умолчанию
        except:
            current_color = QColor(255, 144, 0)

        # Открыть диалог выбора цвета
        color = QColorDialog.getColor(current_color, self, "Выберите цвет")

        if color.isValid():
            # Конвертировать в hex без #
            hex_color = color.name()[1:].upper()
            self.new_color_edit.setText(hex_color)

            # Установить альфа канал
            alpha_hex = f"{color.alpha():02X}"
            self.alpha_edit.setText(alpha_hex)

    def apply_color_changes(self):
        """Применить изменения цвета"""
        try:
            color_type = self.color_type_combo.currentText()
            old_color = ColorTypeManager.get_default_color(color_type)
            new_color = self.new_color_edit.text().strip().upper()
            alpha = self.alpha_edit.text().strip().upper()

            if len(new_color) != 6 or len(alpha) != 2:
                QMessageBox.warning(self, "Ошибка",
                    "Цвет должен быть в формате RRGGBB (6 символов), альфа - 2 символа")
                return

            # Проверить валидность hex
            int(new_color, 16)
            int(alpha, 16)

            # Заменить цвета в содержимом
            old_content = self.file_content
            self.file_content = StyleFileParser.replace_colors(
                self.file_content, old_color, new_color, alpha)

            # Обновить отображение
            self.content_text.setPlainText(self.file_content)

            # Пересканировать цвета
            self.current_colors = StyleFileParser.find_colors(self.file_content)
            self.update_current_color_display()

            # Подсчитать количество замен
            old_count = old_content.count(f'x{alpha}{old_color}') + old_content.count(f'x{alpha}{old_color.lower()}')
            new_count = self.file_content.count(f'x{alpha}{new_color}')
            replacements = new_count - old_count

            QMessageBox.information(self, "Успех",
                f"Выполнено замен: {abs(replacements)}")

        except ValueError:
            QMessageBox.warning(self, "Ошибка", "Неверный формат цвета или альфа канала")
        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при применении изменений: {str(e)}")


def main():
    """Главная функция"""
    app = QApplication(sys.argv)

    # Установить стиль приложения
    app.setStyle('Fusion')

    # Создать и показать главное окно
    window = ColorEditorMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
